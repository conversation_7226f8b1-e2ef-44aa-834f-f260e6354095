from typing import Type
from crewai.tools import BaseTool
from pydantic import BaseModel, Field
import os
# import re # No longer needed for sanitization here
# import datetime # No longer needed for default filename here

class SaveMarkdownToolInput(BaseModel):
    content: str = Field(..., description="The markdown content to save.")
    filename: str = Field(..., description="The full path (including directory and filename) for the markdown file.") # Updated description

class SaveMarkdownTool(BaseTool):
    name: str = "save_markdown_to_file"
    description: str = "Save markdown content to a local file at the specified full path." # Updated description
    args_schema: Type[BaseModel] = SaveMarkdownToolInput

    def _run(self, content: str, filename: str) -> str:
        # The 'filename' argument is now expected to be the full path, e.g., "output/document.md"
        
        # Ensure the directory exists
        # os.path.dirname("output/document.md") -> "output"
        # os.path.dirname("document.md") -> "" (empty string)
        # os.makedirs("", exist_ok=True) is a no-op and safe.
        directory = os.path.dirname(filename)
        if directory: # Only create if directory is not empty
            os.makedirs(directory, exist_ok=True)
        
        # Filename sanitization and .md extension are assumed to be handled before this tool is called
        # (e.g., in api.py's generate_filename_from_url)

        # Default filename logic is removed as 'filename' should always be provided.

        filepath = filename # Use the provided filename directly as the full path

        try:
            with open(filepath, "w", encoding="utf-8") as file:
                file.write(content)
            return f"Markdown content saved successfully to {filepath}"
        except Exception as e:
            return f"Error saving file: {str(e)}"




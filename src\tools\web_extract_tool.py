from typing import Type
from crewai.tools import BaseTool
from pydantic import BaseModel, Field
import re
import requests
from bs4 import BeautifulSoup
from markdownify import markdownify
from requests.exceptions import RequestException
from readability import Document # Import Document for readability

class ExtractWebpageToolInput(BaseModel):
    url: str = Field(..., description="The URL of the webpage to extract.")

def extract_webpage(url: str) -> str:
    try:
        response = requests.get(url, timeout=10)
        response.raise_for_status()

        # Use readability to extract the main content
        doc = Document(response.text)
        readable_html = doc.summary()
        title = doc.title()

        # If readability provides decent content, use it, otherwise fallback.
        # A simple heuristic: check if readable_html is non-trivial.
        if readable_html and len(readable_html) > 200: # Threshold can be adjusted
            main_content_html = readable_html
        else:
            # Fallback to original logic if readability fails or gives too little content
            temp_soup = BeautifulSoup(response.text, 'html.parser')
            found_main = temp_soup.find('main')
            found_article = temp_soup.find('article')
            if found_main:
                main_content_html = str(found_main)
            elif found_article:
                main_content_html = str(found_article)
            else:
                main_content_html = response.text # Fallback to full body if no main/article
        
        # Parse the selected main content (either from readability or fallback)
        soup = BeautifulSoup(main_content_html, 'html.parser')

        # Your existing tag decomposition logic (can act as a secondary cleanup)
        for tag_name in [
            'nav', 'aside', 'header', 'footer', 'script', 'style', 'iframe',
            'button', 'form', 'noscript', 'svg', 'figure', 'figcaption',
            'time', 'meta', 'input', 'textarea', 'hr', 'select', 'option',
            'address', 'dialog', 'menu', 'menuitem', 'summary', 'details']:
            for tag in soup.find_all(tag_name):
                 tag.decompose()

        # Code block processing (remains largely the same, marker based)
        soup_copy = BeautifulSoup(str(soup), 'html.parser') # Use the cleaned soup
        code_blocks = {}
        for i, pre in enumerate(soup_copy.find_all('pre')):
            marker = f"[[[CODEBLOCK{i}]]]"
            code_content = pre.find('code').get_text(separator='\n', strip=False) if pre.find('code') else pre.get_text(separator='\n', strip=False)
            code_blocks[marker] = code_content
            marker_tag = soup_copy.new_tag('p')
            marker_tag.string = marker
            pre.replace_with(marker_tag)
        
        pre_count = len(code_blocks)
        for i, code_tag in enumerate(soup_copy.find_all('code')):
            if code_tag.find_parent('pre') is None: # Process only if not already handled by <pre>
                marker = f"[[[CODEBLOCK{pre_count + i}]]]"
                code_content = code_tag.get_text(separator='\n', strip=False)
                code_blocks[marker] = code_content
                marker_tag = soup_copy.new_tag('p')
                marker_tag.string = marker
                code_tag.replace_with(marker_tag)
        
        markdown_content = markdownify(str(soup_copy)).strip()
        
        for marker, code_text_val in code_blocks.items():
            # This regex cleans excessive blank lines within a code block
            cleaned_code = re.sub(r'\n\s*\n+', '\n', code_text_val).strip()
            markdown_code_block = f"```\n{cleaned_code}\n```"
            markdown_content = markdown_content.replace(marker, markdown_code_block)

        # Revised clean_fenced_code_blocks
        def clean_fenced_code_blocks(md: str) -> str:
            pattern = re.compile(r"```(.*?)```", re.DOTALL)
            def inner(match):
                code_text_inner = match.group(1)
                # Use the gentler regex to preserve single blank lines
                code_text_inner = re.sub(r'\n\s*\n+', '\n', code_text_inner).strip()
                return f"```\n{code_text_inner}\n```"
            return pattern.sub(inner, md)
        
        markdown_content = clean_fenced_code_blocks(markdown_content)
        markdown_content = re.sub(r'\n{3,}', '\n\n', markdown_content) # Final cleanup of excessive newlines
        
        # Optionally prepend title if found by readability
        # if title:
        #    markdown_content = f"# {title}\n\n{markdown_content}"
            
        return markdown_content

    except RequestException as e:
        return f"Error fetching the webpage: {str(e)}"
    except Exception as e:
        import traceback
        return f"An unexpected error occurred during extraction: {str(e)}\n{traceback.format_exc()}" # Added traceback for better debugging

class ExtractWebpageTool(BaseTool):
    name: str = "extract_webpage"
    description: str = "Extract and clean the main content from a webpage and return as markdown."
    args_schema: Type[BaseModel] = ExtractWebpageToolInput

    def _run(self, url: str) -> str:
        return extract_webpage(url)



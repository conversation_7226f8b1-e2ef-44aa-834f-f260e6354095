document.addEventListener('DOMContentLoaded', () => {
  const urlInput = document.getElementById('urlInput');
  const submitBtn = document.getElementById('submitBtn');
  const downloadBtn = document.getElementById('downloadBtn');
  const resultArea = document.getElementById('resultArea');
  const statusDiv = document.getElementById('status');
  let extractedTextContent = '';
  let lastFilename = 'extracted_content.md'; // Default filename

  // Try to get the current tab's URL as a default value
  // Note: This requires "tabs" permission if you want to use it beyond activeTab context.
  // For a popup, activeTab is usually implicitly available for the current page.
  // However, it might not work on special pages (e.g. chrome://newtab).
  if (chrome && chrome.tabs) {
    chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
      if (tabs && tabs[0] && tabs[0].url && !tabs[0].url.startsWith('chrome://')) {
        urlInput.value = tabs[0].url;
      }
    });
  }

  submitBtn.addEventListener('click', async () => {
    if (window.isOverQuota) {
      statusDiv.textContent = 'You are over your monthly quota.';
      return;
    }
    const user = firebase.auth().currentUser;
    if (!user) {
      statusDiv.textContent = 'You must be logged in.';
      return;
    }
    const idToken = await user.getIdToken();
    const url = urlInput.value;
    if (!url || !url.startsWith('http')) {
      statusDiv.textContent = 'Please enter a valid URL (e.g., http://example.com).';
      return;
    }
    statusDiv.textContent = 'Extracting... Please wait.';
    resultArea.value = '';
    downloadBtn.style.display = 'none';
    submitBtn.disabled = true;

    try {
      const response = await fetch('http://localhost:8000/api/extract', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer ' + idToken
        },
        body: JSON.stringify({ url: url }),
      });

      submitBtn.disabled = false;
      const data = await response.json(); // Try to parse JSON regardless of response.ok for error details

      if (!response.ok) {
        throw new Error(data.message || data.detail || `HTTP error! Status: ${response.status}`);
      }
      
      if (data.content) {
        extractedTextContent = data.content;
        lastFilename = data.filename_on_server || lastFilename;
        resultArea.value = extractedTextContent;
        statusDiv.textContent = `Success! Content ready.`;
        downloadBtn.style.display = 'block';
      } else {
        statusDiv.textContent = data.message || 'Extraction failed or no content returned.';
        resultArea.value = data.message;
      }

    } catch (error) {
      console.error('Error:', error);
      statusDiv.textContent = `Error: ${error.message}`;
      resultArea.value = `Failed to connect or process the request.\nIs the Python API server (api.py) running at http://localhost:8000 ?\nDetails: ${error.message}`;
      submitBtn.disabled = false;
    }
  });

  downloadBtn.addEventListener('click', () => {
    if (!extractedTextContent) {
      statusDiv.textContent = 'No content to download.';
      return;
    }
    
    const blob = new Blob([extractedTextContent], { type: 'text/markdown;charset=utf-8' });
    const downloadUrl = URL.createObjectURL(blob);
    
    // Use Chrome Downloads API for a better experience
    chrome.downloads.download({
      url: downloadUrl,
      filename: lastFilename,
      saveAs: true // Optional: set to false to download automatically to default location
    }, (downloadId) => {
      if (chrome.runtime.lastError) {
        console.error('Download failed:', chrome.runtime.lastError.message);
        statusDiv.textContent = 'Download failed. See console.';
        // Fallback for environments where chrome.downloads might be restricted (e.g. some test envs)
        // or if direct download is preferred for some reason.
        const a = document.createElement('a');
        a.href = downloadUrl;
        a.download = lastFilename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
      } else {
        statusDiv.textContent = 'Download started.';
      }
      URL.revokeObjectURL(downloadUrl); // Clean up the object URL
    });
  });

  // === Firebase Auth Integration ===
  // Use config from window.ENV (set in config.js)
  const firebaseConfig = {
    apiKey: window.ENV.FIREBASE_API_KEY,
    authDomain: window.ENV.FIREBASE_AUTH_DOMAIN,
    projectId: window.ENV.FIREBASE_PROJECT_ID,
    storageBucket: window.ENV.FIREBASE_STORAGE_BUCKET,
    messagingSenderId: window.ENV.FIREBASE_MESSAGING_SENDER_ID,
    appId: window.ENV.FIREBASE_APP_ID
  };

  firebase.initializeApp(firebaseConfig);
  setupAuthUI();

  function setupAuthUI() {
    const loginBtn = document.getElementById('loginBtn');
    const signupBtn = document.getElementById('signupBtn');
    const emailInput = document.getElementById('emailInput');
    const passwordInput = document.getElementById('passwordInput');
    const authStatus = document.getElementById('authStatus');

    // Login
    loginBtn.addEventListener('click', async () => {
      const email = emailInput.value;
      const password = passwordInput.value;
      try {
        await firebase.auth().signInWithEmailAndPassword(email, password);
        authStatus.textContent = 'Logged in!';
        checkPaywall();
      } catch (err) {
        authStatus.textContent = 'Login failed: ' + err.message;
      }
    });

    // Signup
    signupBtn.addEventListener('click', async () => {
      const email = emailInput.value;
      const password = passwordInput.value;
      try {
        await firebase.auth().createUserWithEmailAndPassword(email, password);
        authStatus.textContent = 'Account created!';
        checkPaywall();
      } catch (err) {
        authStatus.textContent = 'Signup failed: ' + err.message;
      }
    });

    // Check if already logged in
    firebase.auth().onAuthStateChanged((user) => {
      if (user) {
        authStatus.textContent = 'Logged in!';
        checkPaywall();
      } else {
        authStatus.textContent = 'Please log in.';
      }
    });
  }

  async function checkPaywall() {
    const user = firebase.auth().currentUser;
    if (!user) return;
    const idToken = await user.getIdToken();
    // Call backend to check access
    const res = await fetch('http://localhost:8000/api/check-access', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ id_token: idToken })
    });
    const data = await res.json();
    // Show usage/quota info
    const usageInfo = `Usage: ${data.usage || 0} / ${data.limit || 0}`;
    document.getElementById('main-usage-info').textContent = usageInfo;
    document.getElementById('paywall-usage-info').textContent = usageInfo;
    // Show/hide usage info
    document.getElementById('main-usage-info').style.display = 'block';
    document.getElementById('paywall-usage-info').style.display = 'block';
    if (data.access === true) {
      document.getElementById('paywall').style.display = 'none';
      document.getElementById('mainApp').style.display = 'block';
      window.isOverQuota = false;
    } else {
      document.getElementById('paywall').style.display = 'block';
      document.getElementById('mainApp').style.display = 'none';
      document.getElementById('paywall-message').textContent = data.usage >= data.limit ? 'You are over your monthly quota.' : 'Please subscribe to access.';
      window.isOverQuota = data.usage >= data.limit;
    }

    // Add this after paywall-message
    const subscribeBtn = document.createElement('button');
    subscribeBtn.id = 'subscribeBtn';
    subscribeBtn.textContent = 'Subscribe';
    document.getElementById('paywall').appendChild(subscribeBtn);

    subscribeBtn.addEventListener('click', async () => {
      const user = firebase.auth().currentUser;
      if (!user) {
        alert('You must be logged in.');
        return;
      }
      const idToken = await user.getIdToken();
      // Call backend to create Stripe Checkout session
      const res = await fetch('http://localhost:8000/api/create-checkout-session', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ id_token: idToken })
      });
      const data = await res.json();
      if (data.checkout_url) {
        window.open(data.checkout_url, '_blank');
      } else {
        alert('Failed to create checkout session.');
      }
    });
  }

  document.getElementById('refreshAccessBtn').addEventListener('click', () => {
    checkPaywall();
  });
}); 
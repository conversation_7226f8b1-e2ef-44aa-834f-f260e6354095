Developer resources
===================

Get up and running with SDKs, API keys, and integration tools.
--------------------------------------------------------------

Before you begin, set up your [development environment](/get-started/development-environment).

Get started

Use Stripe’s libraries and tools to build and manage your integration.

[Install an SDK](/sdks)

![](https://b.stripecdn.com/docs-statics-srv/assets/stripe-cli.dfdd7710717be8cb82486c38d77d4572.png)

Versioning
----------

[Changelog

Review breaking changes and new features.](/changelog "Changelog")

[API upgrades

Keep track of changes and upgrades to the Stripe API.](/upgrades "API upgrades")

[SDK versioning

Learn how Stripe versions its APIs and SDKs.](/sdks/versioning "SDK versioning")

Essentials
----------

[SDKs

Use client, server, and UI SDKs to integrate with Stripe.](/sdks "SDKs")

[API management and error handling

Use the API to authenticate requests and respond to errors.](/keys "API management and error handling")

[Testing

Test your integration by simulating payments.](/testing "Testing")

Tools
-----

[Workbench

Debug, manage, and grow your Stripe integration.](/workbench "Workbench")

[Developers Dashboard

View API request and event activity.](/development/dashboard "Developers Dashboard")

[Stripe for Visual Code

Build, test, and use Stripe inside Visual Studio Code.](/stripe-vscode "Stripe for Visual Code")

Features
--------

[Workflows

Learn how to automate workflows without code in the Dashboard.](/workflows "Workflows")

[Event destinations

Send events from Stripe to webhook endpoints and cloud services.](/event-destinations "Event destinations")

[Stripe health alerts

Monitor the health of your API integrations through automated alerts.](/health-alerts "Stripe health alerts")

AI solutions
------------

[Agent toolkit

Use Stripe to run your agent business and enhance your agents’ functionality.](/agents "Agent toolkit")

[Build with an LLM

Use a large language model (LLM) to help you build and manage a Stripe integration.](/building-with-llms "Build with an LLM")

Security and privacy
--------------------

[Security

Learn how Stripe handles security.](/security "Security")

[Privacy

Remove data from your Dashboard and API.](/privacy/deletion-requests "Privacy")

Extend Stripe
-------------

[Stripe Apps

Extend Stripe with third-party services or embed custom user experiences in the Stripe Dashboard.](/stripe-apps "Stripe Apps")

[Stripe Apps

Use apps to integrate with Stripe.](/use-stripe-apps "Stripe Apps")

Partners
--------

[Stripe Partner Ecosystem

Join the community of Stripe partners that helps businesses with payments and financial infrastructure.](/partners "Stripe Partner Ecosystem")

[Stripe certification for partners

Become a Stripe-certified architect or developer.](/partners/training-and-certification "Stripe certification for partners")

Developer community
-------------------

[YouTube tutorials](https://www.youtube.com/stripedevelopers)Watch developer tutorials on our YouTube channel.[![](https://b.stripecdn.com/docs-statics-srv/assets/stripe-developers-youtube.9ea4c6da87382179e1248f9df851ff66.png)](https://www.youtube.com/stripedevelopers)

* [Developer tools](https://stripe.dev/#developer-digest)

  Sign up for the developer newsletter to get highlights and updates.
* [X updates](https://x.com/stripe)

  Follow us on X (formerly Twitter) to get updates and join our community.
* [Developer chat on Discord](https://stripe.com/go/developer-chat)

  Chat live with other developers on the official Stripe Discord.
import os
import stripe
from fastapi import APIRouter, Request, HTTPException, status
from fastapi.responses import JSONResponse
from dotenv import load_dotenv

load_dotenv()

STRIPE_SECRET_KEY = os.getenv("STRIPE_SECRET_KEY")
STRIPE_WEBHOOK_SECRET = os.getenv("STRIPE_WEBHOOK_SECRET")
stripe_client = stripe.StripeClient(STRIPE_SECRET_KEY)

router = APIRouter(prefix="/payments", tags=["payments"])

@router.post("/create-payment-intent")
async def create_payment_intent(data: dict):
    try:
        amount = int(data.get("amount", 0))  # Amount in cents
        currency = data.get("currency", "usd")
        if amount <= 0:
            raise HTTPException(status_code=400, detail="Invalid amount")
        intent = stripe_client.payment_intents.create(
            amount=amount,
            currency=currency,
            automatic_payment_methods={"enabled": True},
        )
        return {"clientSecret": intent.client_secret}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/webhook")
async def stripe_webhook(request: Request):
    payload = await request.body()
    sig_header = request.headers.get("stripe-signature")
    event = None
    # Retry logic: Stripe will retry failed webhooks automatically, but you can add custom logic if needed.
    try:
        event = stripe_client.webhooks.construct_event(
            payload, sig_header, STRIPE_WEBHOOK_SECRET
        )
    except stripe.error.SignatureVerificationError:
        raise HTTPException(status_code=400, detail="Invalid signature")
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Webhook error: {str(e)}")

    # Handle the event
    if event["type"] == "payment_intent.succeeded":
        payment_intent = event["data"]["object"]
        # Fulfill the purchase, update DB, etc.
        print(f"PaymentIntent succeeded: {payment_intent['id']}")
    elif event["type"] == "payment_intent.payment_failed":
        payment_intent = event["data"]["object"]
        print(f"PaymentIntent failed: {payment_intent['id']}")
    else:
        print(f"Unhandled event type: {event['type']}")

    return JSONResponse({"status": "success"})
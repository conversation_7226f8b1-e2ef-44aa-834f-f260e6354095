import uvicorn
from fastapi import <PERSON><PERSON><PERSON>, Request, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import os
import re
from urllib.parse import urlparse
import sys # Import sys for stdout
import logging
from .crew import ChromeExtensionExtractor 
from .payments import router as payments_router
import firebase_admin
from firebase_admin import auth, credentials, firestore
from stripe import StripeClient
from typing import Dict
from datetime import datetime
import stripe  # Ensure this is at the top

logging.basicConfig(stream=sys.stdout, level=logging.DEBUG)

# Ensure output directory exists (still relevant if any test writes files)
os.makedirs("output", exist_ok=True)

# === CONFIGURATION ===
# TODO: Replace with your actual Firebase service account key path
FIREBASE_SERVICE_ACCOUNT = os.getenv('FIREBASE_SERVICE_ACCOUNT', 'path/to/serviceAccountKey.json')
# TODO: Replace with your actual Stripe secret key
STRIPE_SECRET_KEY = os.getenv('STRIPE_SECRET_KEY', 'sk_test_...')
STRIPE_PRICE_ID = os.getenv('STRIPE_PRICE_ID', 'price_...')

# Initialize Firebase Admin
if not firebase_admin._apps:
    cred = credentials.Certificate(FIREBASE_SERVICE_ACCOUNT)
    firebase_admin.initialize_app(cred)

# Initialize Stripe
stripe_client = StripeClient(STRIPE_SECRET_KEY)

app = FastAPI()


# For production, you might want to restrict origins to your extension's ID.
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allows all origins
    allow_credentials=True,
    allow_methods=["*"],  # Allows all methods (GET, POST, OPTIONS, etc.)
    allow_headers=["*"],  # Allows all headers
)

app.include_router(payments_router)

class UrlInput(BaseModel):
    url: str

class ExtractionResponse(BaseModel):
    filename_on_server: str
    content: str
    message: str

# --- Test Pydantic Model --- 
class SimpleOutput(BaseModel):
    test_key: str
    another_key: int

class TokenRequest(BaseModel):
    id_token: str

class CheckoutRequest(BaseModel):
    id_token: str

# === Firestore Initialization ===
db = firestore.client()
USERS_COLLECTION = "users"

USAGE_LIMIT_PER_MONTH = 100  # Example plan limit

def get_current_month():
    return datetime.utcnow().strftime('%Y-%m')

def get_usage(uid, month=None):
    if month is None:
        month = get_current_month()
    user = get_user_by_uid(uid)
    if user and 'usage' in user and month in user['usage']:
        return user['usage'][month]
    return 0

def increment_usage(uid, month=None):
    if month is None:
        month = get_current_month()
    doc_ref = db.collection(USERS_COLLECTION).document(uid)
    doc = doc_ref.get()
    if doc.exists:
        usage = doc.to_dict().get('usage', {})
        usage[month] = usage.get(month, 0) + 1
        doc_ref.update({'usage': usage})
    else:
        usage = {month: 1}
        doc_ref.set({'usage': usage}, merge=True)

def get_user_by_uid(uid):
    doc_ref = db.collection(USERS_COLLECTION).document(uid)
    doc = doc_ref.get()
    if doc.exists:
        return doc.to_dict()
    return None

def create_user(uid, email, stripe_customer_id):
    doc_ref = db.collection(USERS_COLLECTION).document(uid)
    doc_ref.set({
        "firebase_uid": uid,
        "email": email,
        "stripe_customer_id": stripe_customer_id
    })
    return {
        "firebase_uid": uid,
        "email": email,
        "stripe_customer_id": stripe_customer_id
    }

def update_user(uid, **fields):
    doc_ref = db.collection(USERS_COLLECTION).document(uid)
    doc_ref.update(fields)

def generate_filename_from_url(url: str) -> str:
    """Generates a safe filename from a URL."""
    parsed_url = urlparse(url)
    domain = parsed_url.netloc
    path = parsed_url.path.replace('/', '_')
    if not path or path == '_':
        path = '_homepage'
    
    # Sanitize domain and path
    safe_domain = re.sub(r'[^a-zA-Z0-9_.-]', '', domain)
    safe_path = re.sub(r'[^a-zA-Z0-9_-]', '', path)
    
    filename = f"{safe_domain}{safe_path}.md"
    # Limit filename length
    return filename[:100]

@app.post("/api/extract", response_model=ExtractionResponse)
async def extract_content(item: UrlInput, request: Request):
    # Extract Firebase ID token from Authorization header
    auth_header = request.headers.get('authorization')
    if not auth_header or not auth_header.lower().startswith('bearer '):
        return ExtractionResponse(
            filename_on_server="",
            content="",
            message="Missing or invalid Authorization header."
        )
    id_token = auth_header.split(' ', 1)[1]
    try:
        decoded = auth.verify_id_token(id_token)
        uid = decoded['uid']
        # Increment usage for this user/month
        increment_usage(uid)
    except Exception as e:
        return ExtractionResponse(
            filename_on_server="",
            content="",
            message=f"Auth error: {str(e)}"
        )
    target_url = item.url
    
    # Generate the base filename
    base_filename = generate_filename_from_url(target_url)
    # Prepend the output directory for the crew
    output_filename_for_crew = os.path.join("output", base_filename)
    
    crew_inputs = {
        'target_url': target_url,
        'output_filename': output_filename_for_crew # Pass the full path to the crew
    }

    print(f"[API LOG] Preparing to kickoff crew with inputs: {crew_inputs}", file=sys.stderr)

    try:
        crew_instance = ChromeExtensionExtractor()
        print("[API LOG] Crew instance created.", file=sys.stderr)
        
        result = crew_instance.crew().kickoff(inputs=crew_inputs)
        
        print(f"[API LOG] Crew kickoff finished. Raw result object type: {type(result)}, value: {result}", file=sys.stderr)
        
        # The path to check is now the same as what was passed to the crew
        saved_file_path = output_filename_for_crew

        if os.path.exists(saved_file_path):
            with open(saved_file_path, 'r', encoding='utf-8') as f:
                extracted_content = f.read()
            return ExtractionResponse(
                filename_on_server=base_filename,
                content=extracted_content,
                message="Content extracted and retrieved successfully."
            )
        else:
           
            final_output_message = str(result) # Get string representation of the final output

            if "successfully written" in final_output_message.lower():
                 return ExtractionResponse(
                    filename_on_server=base_filename,
                    content="", # No content to return as file is missing
                    message=f"Crew reported success ({final_output_message}), but file {saved_file_path} not found on server."
                )
            else:
                 return ExtractionResponse(
                    filename_on_server=base_filename,
                    content="", # No content to return
                    message=f"Content extraction failed or file not written. Final crew output: {final_output_message}"
                )

    except Exception as e:
        import traceback
        traceback.print_exc(file=sys.stderr)
        return ExtractionResponse(
            filename_on_server=base_filename,
            content="",
            message=f"An error occurred: {str(e)}"
        )

@app.post('/api/verify-auth')
def verify_auth(req: TokenRequest):
    try:
        decoded = auth.verify_id_token(req.id_token)
        return {"uid": decoded['uid'], "email": decoded.get('email')}
    except Exception as e:
        raise HTTPException(status_code=401, detail=str(e))

def has_active_subscription(stripe_client, customer_id):
    for status in ['active', 'trialing', 'past_due']:
        search_results = stripe_client.subscriptions.search({
            "query": f"customer:'{customer_id}' AND status:'{status}'",
            "limit": 1
        })
        if search_results.data:
            return True
    return False

@app.post('/api/check-access')
def check_access(req: TokenRequest):
    try:
        print(f"Received ID token: {req.id_token[:30]}...")  # Print first 30 chars for privacy
        decoded = auth.verify_id_token(req.id_token)
        print(f"Decoded token: {decoded}")
        uid = decoded['uid']
        email = decoded.get('email')
        if not email:
            raise HTTPException(status_code=400, detail='No email in Firebase token')
        # Lookup user in Firestore
        user = get_user_by_uid(uid)
        if user:
            stripe_customer_id = user['stripe_customer_id']
        else:
            # Use Stripe Search API to find customer by email
            search_results = stripe_client.customers.search({
                "query": f"email:'{email}'",
                "limit": 1
            })
            if search_results.data:
                stripe_customer_id = search_results.data[0].id
            else:
                # Create new Stripe customer
                customer = stripe_client.customers.create(email=email, metadata={"firebase_uid": uid})
                stripe_customer_id = customer.id
            # Store mapping in Firestore
            create_user(uid, email, stripe_customer_id)
        # Check for active subscription
        has_active = has_active_subscription(stripe_client, stripe_customer_id)
        # Usage/overage logic
        usage = get_usage(uid)
        over_quota = usage >= USAGE_LIMIT_PER_MONTH
        if has_active and not over_quota:
            return {"access": True, "usage": usage, "limit": USAGE_LIMIT_PER_MONTH}
        else:
            return {"access": False, "usage": usage, "limit": USAGE_LIMIT_PER_MONTH}
    except Exception as e:
        print(f"Token verification error: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=401, detail=str(e))

@app.post('/api/create-checkout-session')
def create_checkout_session(req: CheckoutRequest):
    try:
        decoded = auth.verify_id_token(req.id_token)
        uid = decoded['uid']
        email = decoded.get('email')
        # Debug prints for troubleshooting
        print(f"STRIPE_SECRET_KEY: {os.getenv('STRIPE_SECRET_KEY')}")
        print(f"STRIPE_PRICE_ID: {os.getenv('STRIPE_PRICE_ID')}")
        print(f"Email: {email}")
        # TODO: Lookup or create Stripe customer for this user
        # TODO: Use STRIPE_PRICE_ID for subscription
        session = stripe_client.checkout.sessions.create({
            "mode": "subscription",
            "line_items": [{"price": os.getenv('STRIPE_PRICE_ID'), "quantity": 1}],
            "customer_email": email,
            "success_url": "https://your-extension.com/success",
            "cancel_url": "https://your-extension.com/cancel"
            # "payment_method_collection": "always",  # Optional
        })
        return {"checkout_url": session.url}
    except Exception as e:
        print(f"Stripe error: {e}")
        raise HTTPException(status_code=400, detail=str(e))

@app.post('/api/webhook')
async def stripe_webhook(request: Request):
    print("Webhook received")
    payload = await request.body()
    sig_header = request.headers.get('stripe-signature')
    webhook_secret = os.getenv('STRIPE_WEBHOOK_SECRET', 'whsec_...')
    print(f"Signature header: {sig_header}")
    print(f"Webhook secret: {webhook_secret}")
    print(f"Payload: {payload}")
    try:
        event = stripe.Webhook.construct_event(
            payload, sig_header, webhook_secret
        )
        print(f"Webhook event: {event['type']}")
    except Exception as e:
        print(f"Webhook error: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=400, detail=f"Webhook error: {e}")

    if event['type'] == 'checkout.session.completed':
        session = event['data']['object']
        customer_email = session.get('customer_email')
        print(f"Webhook: checkout.session.completed for {customer_email}")
        users = db.collection(USERS_COLLECTION).where('email', '==', customer_email).stream()
        for user_doc in users:
            user_doc.reference.update({'subscribed': True})
            print(f"Updated user {user_doc.id} as subscribed.")
    return {"status": "received"}

if __name__ == "__main__":
    # For production, you might use a process manager like Gunicorn with Uvicorn workers
    uvicorn.run(app, host="0.0.0.0", port=8000) 
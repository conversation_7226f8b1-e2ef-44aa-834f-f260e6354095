<!DOCTYPE html>
<html>
<head>
  <title>Grabbr.AI</title>
  <link rel.stylesheet href="popup.css">
  <meta charset="UTF-8">
</head>
<body>
  <div id="auth-container">
    <input type="email" id="emailInput" placeholder="Email" />
    <input type="password" id="passwordInput" placeholder="Password" />
    <button id="loginBtn">Login</button>
    <button id="signupBtn">Sign Up</button>
    <div id="authStatus"></div>
  </div>
  <div id="usage-info" style="margin: 8px 0; color: #333; display: none;"></div>
  <div id="paywall" style="display:none;">
    <div id="paywall-message">Please subscribe to access.</div>
    <div id="paywall-usage-info" style="margin: 8px 0; color: #b00;"></div>
    <button id="refreshAccessBtn">Refresh Access</button>
    <!-- Stripe payment UI can be added here -->
  </div>
  <div id="mainApp" style="display:none;">
    <div id="main-usage-info" style="margin: 8px 0; color: #333;"></div>
    <div class="container">
      <h1>Web Content Extractor</h1>
      <input type="url" id="urlInput" placeholder="Enter URL here">
      <button id="submitBtn">Extract Content</button>
      <div id="status"></div>
      <textarea id="resultArea" readonly placeholder="Extracted content will appear here..."></textarea>
      <button id="downloadBtn" style="display:none;">Download as Markdown</button>
      <form id="payment-form">
        <div id="card-element"><!-- Stripe Card Element will be inserted here --></div>
        <button id="submit">Pay</button>
        <div id="payment-message"></div>
      </form>
    </div>
  </div>
  <script src="vendor/firebase-app-compat.js"></script>
  <script src="vendor/firebase-auth-compat.js"></script>
  <script src="config.js"></script>
  <script src="popup.js"></script>
  
</body>
</html> 
body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
  width: 320px; /* Slightly wider for better layout */
  padding: 15px;
  background-color: #f4f6f8;
  color: #333;
}

.container {
  display: flex;
  flex-direction: column;
}

h1 {
  font-size: 18px;
  text-align: center;
  color: #2c3e50;
  margin-top: 0;
  margin-bottom: 15px;
}

input[type="url"] {
  width: 100%;
  padding: 8px;
  margin-bottom: 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
  box-sizing: border-box; /* Ensures padding doesn't add to width */
}

button {
  padding: 10px 15px;
  margin-bottom: 10px;
  background-color: #3498db;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

button:hover {
  background-color: #2980b9;
}

#submitBtn {
    background-color: #2ecc71; /* Green for primary action */
}
#submitBtn:hover {
    background-color: #27ae60;
}

textarea {
  width: 100%;
  height: 120px; /* Increased height */
  margin-bottom: 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
  padding: 8px;
  box-sizing: border-box;
  font-family: monospace;
}

#status {
  margin-bottom: 10px;
  font-style: italic;
  font-size: 13px;
  min-height: 1.2em; /* Reserve space to prevent layout shifts */
  text-align: center;
}

#downloadBtn {
  background-color: #e67e22;
}
#downloadBtn:hover {
  background-color: #d35400;
} 
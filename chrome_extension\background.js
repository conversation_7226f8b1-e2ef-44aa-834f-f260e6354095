chrome.action.onClicked.addListener((tab) => {
  // Check if a tab with your extension's page is already open
  const extensionPageUrl = chrome.runtime.getURL("popup.html");
  chrome.tabs.query({ url: extensionPageUrl }, (tabs) => {
    if (tabs.length > 0) {
      // If already open, focus that tab
      chrome.tabs.update(tabs[0].id, { active: true });
      // Optionally, if the window is not focused, focus the window
      chrome.windows.update(tabs[0].windowId, { focused: true });
    } else {
      // If not open, create a new tab
      chrome.tabs.create({ url: "popup.html" });
    }
  });
}); 
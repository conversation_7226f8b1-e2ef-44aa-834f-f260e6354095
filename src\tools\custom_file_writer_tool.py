from typing import Type, Optional
import os
from pydantic import BaseModel, Field
from crewai.tools import BaseTool

class CustomFileWriterToolInput(BaseModel):
    """Input for CustomFileWriterTool."""
    file_path: str = Field(..., description="The path to the file to write.")
    text: str = Field(..., description="The text content to write to the file.")

class CustomFileWriterTool(BaseTool):
    name: str = "Custom File Writer"
    description: str = "Writes text content to a specified file using UTF-8 encoding. Overwrites if the file exists."
    args_schema: Type[BaseModel] = CustomFileWriterToolInput

    def _run(
        self,
        file_path: str,
        text: str,
    ) -> str:
        try:
            # Ensure the directory exists
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(text)
            return f"Successfully wrote content to {file_path}"
        except Exception as e:
            return f"Error writing file {file_path}: {e}" 
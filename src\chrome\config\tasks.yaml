url_acquisition_task:
  description: >
    Validate the provided {target_url} and {output_filename}. 
    If {output_filename} is not suitable or missing, refine it based on {target_url} (e.g., 'domain_com_content.md').
    These will be used for content extraction and saving.
  expected_output: >
    A dictionary or structured string containing the validated (and possibly refined) 'target_url' and 'output_filename' to be used by subsequent tasks.
  agent: manager
  

extraction_task:
  description: >
    Your primary goal is to extract the main textual content from the website at {target_url}.
    Use the ExtractWebpageTool for this.
    IMPORTANT: Your final answer for this task MUST be ONLY the extracted textual content itself.
    Do NOT include any introductory phrases, explanations, summaries, or any other text surrounding the content.
    Just the raw, extracted text. This will be automatically structured by the system.
  expected_output: >
    The raw, unadulterated textual content from the main body of the page {target_url}. 
    This output is expected to directly populate the 'text_content' field of a Pydantic model.
  agent: extractor
  # The context for {target_url} will come from url_acquisition_task's output.

export_task:
  description: >
    Save the extracted text content to a markdown file named: {output_filename}.
    The text content will be available from the output of the content extraction task.
    Ensure you use the SaveMarkdownTool (or appropriate file saving tool) for this purpose.
  expected_output: >
    A confirmation message indicating the file path where the content was saved, using the filename {output_filename}.
  agent: saver
  # The context for {text_content} will come from extraction_task's output.
  # The context for {output_filename} will come from url_acquisition_task's output.


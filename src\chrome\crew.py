import os
import yaml
import logging
import sys
import time
from typing import List, Dict, Any
from pydantic import BaseModel
from crewai import Agent, Crew, Process, Task, LLM
from crewai.project import CrewBase, agent, crew, task
from crewai.agents.agent_builder.base_agent import BaseAgent
# from crewai_tools import ScrapeWebsiteTool, FileWriterTool # No longer needed here if all tools are custom

# Import the custom tool
from tools.web_extract_tool import ExtractWebpageTool
# from src.tools.custom_file_writer_tool import CustomFileWriterTool # No longer using this
from tools.save_md_tool import SaveMarkdownTool # Import your tool

logging.basicConfig(level=logging.INFO)

# Define the Pydantic model for the extraction task output
class TextContentOutput(BaseModel):
    text_content: str

# Tool instances
extract_webpage_tool = ExtractWebpageTool() 
save_markdown_tool = SaveMarkdownTool() # Instantiate your tool

llm = LLM(
    model="gemini/gemini-2.5-flash-preview-04-17",
    temperature=.2,
    thinking={"type": "enabled", "budget_tokens": 1024},
)



@CrewBase
class ChromeExtensionExtractor:
    """Chrome Extension Extractor Crew"""

    agents_config = 'config/agents.yaml'
    tasks_config = 'config/tasks.yaml'

    # Removed __init__ and self.url as manager task will handle URL input

    @agent
    def manager(self) -> Agent:
        return Agent(
            config=self.agents_config['manager'], # Use 'manager' key from agents.yaml
            allow_delegation=True,
            llm=llm
        )

    @agent
    def extractor(self) -> Agent:
        return Agent(
            config=self.agents_config['extractor'],
            tools=[extract_webpage_tool],
            llm=llm
        )

    @agent
    def saver(self) -> Agent:
        return Agent(
            config=self.agents_config['saver'],
            tools=[save_markdown_tool], # Use SaveMarkdownTool
            llm=llm
        )

    @task
    def url_acquisition_task(self) -> Task:
        return Task(
            config=self.tasks_config['url_acquisition_task'],
            agent=self.manager(),
            # human_input=True is set in tasks.yaml
        )

    @task
    def extraction_task(self) -> Task:
        return Task(
            config=self.tasks_config['extraction_task'],
            agent=self.extractor(),
            context=[self.url_acquisition_task()],
            output_pydantic=TextContentOutput
        )

    @task
    def export_task(self) -> Task:

        return Task(
            config=self.tasks_config['export_task'], # Use the modified config
            agent=self.saver(),
            context=[self.extraction_task(), self.url_acquisition_task()]
        )

    @crew
    def crew(self) -> Crew:
        """Creates the crew with a manager orchestrating tasks."""
        return Crew(
            agents=[self.extractor(), self.saver()],
            tasks=[self.url_acquisition_task(), self.extraction_task(), self.export_task()],
            process=Process.sequential,
            max_rpm=5,
            llm=llm,
            manager_agent=self.manager()
        )


    
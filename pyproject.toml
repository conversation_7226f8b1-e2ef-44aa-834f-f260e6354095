[project]
name = "Grabbr.AI"
version = "0.1.0"
description = "Grabbr.AI is a Chrome extension that allows you to extract documentation from a website."
authors = [{ name = "Grabbr.AI" }]
requires-python = ">=3.10,<3.13"
dependencies = [
    "crewai==0.119.0",
    "typing-extensions>=4.13.2",
    "pytz>=2024.1,<2025.0",
    "protobuf>=5.0,<6.0",
    "chromadb>=1.0.0",
    "setuptools",
    "fastapi",
    "uvicorn",
    "python-multipart",
    "python-dotenv",
    "pydantic",
    "pyyaml",
    "onnxruntime",
    "beautifulsoup4",
    "requests",
    "markdownify",
    "readability-lxml",
    "stripe",
    "gunicorn",
    "firebase-admin"
]

[project.scripts]
chrome = "src.chrome.main:run"
run_crew = "src.chrome.main:run"
train = "src.chrome.main:train"
replay = "src.chrome.main:replay"
test = "src.chrome.main:test"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.crewai]
type = "crew"

[tool.hatch.build.targets.wheel]
packages = ["src/chrome"]

#!/usr/bin/env python
# src/research_crew/main.py
import os
from .crew import ChromeExtensionExtractor

# Create output directory if it doesn't exist
os.makedirs('output', exist_ok=True)

def run():
    """
    Run the chrome extension extractor crew.
    """
   

    # Create and run the crew
    result = ChromeExtensionExtractor().crew().kickoff()

    # Print the result
    print("\n\n=== Enter URL ===\n\n")
   

    print("\n\nReport has been saved to output/report.md")

if __name__ == "__main__":
    run()